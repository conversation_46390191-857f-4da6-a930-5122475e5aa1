<?php

namespace App\Http\Controllers\front;

use App\Http\Controllers\Controller;
use App\Jobs\ProcessProductMedia;
use App\Models\BannerAd;
use App\Models\Brand;
use App\Models\Category;
use App\Models\Follow;
use App\Models\Location;
use App\Models\Product;
use App\Models\ProductComment;
use App\Models\ProductAnalytics;
use App\Models\Tag;
use App\Models\User;
use App\Models\UserSession;
use App\Models\NewsletterSubscriber;
use App\Models\ProductMedia;
use App\Models\Report;
use App\Models\Commission;
use App\Services\CommissionService;
use App\Enums\PaymentMethodEnum;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

class Front2Controller extends Controller
{
    /**
     * Display the homepage with featured listings
     */
    public function index(Request $request)
    {
        // Get featured categories (top level categories)
        $featuredCategories = Category::whereNull('parent_id')
            ->orderBy('order')
            ->limit(8)
            ->get();

        // Get banner ads
        $bannerAds = BannerAd::where('is_active', true)
            ->where('screen', 'home')
            ->orderBy('order')
            ->get();

        // Get locations for filter
        $locations = \App\Models\Location::orderBy('name')->get();

        return view('web.home.index', compact('featuredCategories', 'bannerAds', 'locations'));
    }

    /**
     * Set the application locale
     */
    public function setLocale(Request $request, $locale)
    {
        if (in_array($locale, ['en', 'ar'])) {
            App::setLocale($locale);
            session()->put('locale', $locale);
        }

        return redirect()->back();
    }

    /**
     * Set the theme (dark/light)
     */
    public function setTheme(Request $request, $theme)
    {
        if (in_array($theme, ['light', 'dark'])) {
            session()->put('theme', $theme);
        }

        return redirect()->back();
    }

    /**
     * Display all categories
     */
    public function categories()
    {
        // Get all parent categories with their children and product counts
        $parentCategories = Category::whereNull('parent_id')
            ->with(['children', 'products'])
            ->orderBy('order')
            ->get();

        // Calculate total products count for each parent category (including children)
        foreach ($parentCategories as $parent) {
            $childrenIds = $parent->children->pluck('id')->toArray();
            $directProductsCount = $parent->products->count();

            // Count products in child categories
            $childrenProductsCount = 0;
            if (!empty($childrenIds)) {
                $childrenProductsCount = Product::whereIn('category_id', $childrenIds)->count();
            }

            // Set the total count
            $parent->total_products_count = $directProductsCount + $childrenProductsCount;

            // Calculate products count for each child category
            foreach ($parent->children as $child) {
                $child->products_count = $child->products->count();
            }
        }

        return view('web.categories', compact('parentCategories'));
    }

    /**
     * Display products in a specific category
     */
    public function category(Request $request, $slug)
    {
        $category = Category::where('slug', $slug)->firstOrFail();
        $locations = Location::orderBy('name')->get();

        // Get child categories with product counts
        $childCategories = Category::where('parent_id', $category->id)
            ->withCount('products')
            ->orderBy('title')
            ->get();

        return view('web.category', compact('category', 'locations', 'childCategories'));
    }

    /**
     * Search for products
     */
    public function search(Request $request)
    {
        $categories = Category::whereNull('parent_id')->orderBy('order')->get();
        $locations = Location::orderBy('name')->get();

        return view('web.search', compact('categories', 'locations'));
    }

    /**
     * Display product details
     */
    public function product($slug)
    {
        $product = Product::where('slug', $slug)
            ->with(['user', 'category', 'location', 'tags', 'media', 'comments.user'])
            ->firstOrFail();

        // Increment view count
        $product->incrementViews();

        // Get related products
        $relatedProducts = Product::where('id', '!=', $product->id)
            ->where('category_id', $product->category_id)
            ->with(['user', 'category', 'media'])
            ->orderBy('created_at', 'desc')
            ->limit(6)
            ->get();

        // Get seller's other products
        $sellerProducts = Product::where('id', '!=', $product->id)
            ->where('user_id', $product->user_id)
            ->with(['category', 'media'])
            ->orderBy('created_at', 'desc')
            ->limit(4)
            ->get();

        return view('web.product', compact('product', 'relatedProducts', 'sellerProducts'));
    }

    /**
     * Display seller profile
     */
    public function sellerProfile($username)
    {
        $seller = User::where('username', $username)
            ->with(['sessions', 'products'])
            ->firstOrFail();

        return view('web.seller_profile', compact('seller'));
    }

    /**
     * Display contact page
     */
    public function contactUs()
    {
        return view('web.contact');
    }

    /**
     * Display about page
     */
    public function about()
    {
        return view('web.about');
    }

    /**
     * Display user favorites
     */
    public function favorites()
    {
        if (!Auth::check()) {
            return redirect()->route('web.auth.login');
        }

        $user = Auth::user();
        $favorites = $user->favorites()
            ->with(['category', 'media'])
            ->paginate(24);

        return view('web.profile.favorites', compact('favorites'));
    }

    /**
     * Toggle product favorite status
     */
    public function toggleFavorite($productId)
    {
        if (!Auth::check()) {
            return response()->json(['success' => false, 'message' => 'User not authenticated'], 401);
        }

        $user = Auth::user();
        $product = Product::findOrFail($productId);

        if ($user->favorites()->where('product_id', $product->id)->exists()) {
            $user->favorites()->detach($product->id);
            $isFavorite = false;
        } else {
            $user->favorites()->attach($product->id);
            $isFavorite = true;
        }

        return response()->json([
            'success' => true,
            'isFavorite' => $isFavorite
        ]);
    }

    /**
     * Display product creation form
     */
    public function createProduct()
    {
        if (!Auth::check()) {
            return redirect()->route('web.auth.login');
        }

        // Check if email is verified
        if (!Auth::user()->hasVerifiedEmail()) {
            return redirect()->route('web.auth.verification.notice');
        }

        $categories = Category::whereNull('parent_id')->with('children')->get();
        $locations = Location::orderBy('name')->get();

        return view('web.create_product', compact('categories', 'locations'));
    }

    /**
     * Store a new product
     */
    public function storeProduct(Request $request)
    {
        if (!Auth::check()) {
            return redirect()->route('web.auth.login');
        }

        // Check if email is verified
        if (!Auth::user()->hasVerifiedEmail()) {
            return redirect()->route('web.auth.verification.notice');
        }

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'desc' => 'required|string',
            'price' => 'nullable|numeric|min:0',
            'category_id' => 'required|exists:categories,id',
            'location_id' => 'required|exists:locations,id',
            'condition' => 'required|string',
            'images.*' => 'image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        $product = new Product();
        $product->title = $validated['title'];
        $product->desc = $validated['desc'];
        $product->price = $validated['price'];
        $product->category_id = $validated['category_id'];
        $product->location_id = $validated['location_id'];
        $product->condition = $validated['condition'];
        $product->user_id = Auth::id();
        $product->save();

        // Handle image uploads
         // Handle image uploads
            if ($request->hasFile('images')) {
                foreach ($request->file('images') as $index => $image) {
                    $fileOriginalName = $image->getClientOriginalName();
                    $fileSize = $image->getSize();
                    $fileMimeType = $image->getClientMimeType();
                    $fileExtension = $image->getClientOriginalExtension();
                    $fileName = time() . '_' . $index . '.' . $fileExtension;

                    // Store in product-specific directory
                    $path = "products/{$product->id}/images/" . $fileName;
                    Storage::disk('products')->put($path, file_get_contents($image));

                    $media = new ProductMedia();
                    $media->name = $fileName;
                    $media->type = 'image';
                    $media->mime_type = $fileMimeType;
                    $media->file_size = $fileSize;
                    $media->extension = $fileExtension;
                    $media->path = $path;
                    $media->order = $index;
                    $media->is_primary = $index === 0;
                    $media->product_id = $product->id;
                    $media->save();

                    // Dispatch job to process the media
                    ProcessProductMedia::dispatch($media, $path);
                }
            }

        return redirect()->route('web.product', $product->slug)->with('success', 'Product created successfully!');
    }

    /**
     * Add a comment to a product
     */
    public function addComment(Request $request, Product $product)
    {
        if (!Auth::check()) {
            return redirect()->route('web.auth.login');
        }
        $validated = $request->validate([
            'content' => 'required|string|max:1000',
            'parent_id' => 'nullable|exists:product_comments,id',
        ]);

        $comment = new ProductComment();
        $comment->product_id = $product->id;
        $comment->user_id = Auth::id();
        $comment->text = $validated['content'];
        $comment->parent_id = $validated['parent_id'] ?? null;
        $comment->save();

        // If this is a reply, notify the parent comment author
        if ($comment->parent_id) {
            $this->notifyCommentReply($comment->parent, $comment);
        } else {
            // If this is a new comment, notify users who follow this product
            $this->notifyProductFollowers($product, $comment);
        }

        return redirect()->back()->with('success', __('تم إضافة التعليق بنجاح!'));
    }

    /**
     * Notify users who follow a product when a new comment is added
     */
    private function notifyProductFollowers(Product $product, ProductComment $comment)
    {
        // Skip notification if the commenter is the product owner
        if ($comment->user_id == $product->user_id) {
            return;
        }

        // Get all users who follow this product
        $followers = Follow::where('followable_type', Product::class)
            ->where('followable_id', $product->id)
            ->get();

        // Notify each follower
        foreach ($followers as $follower) {
            // Skip notification to the commenter
            if ($follower->user_id == $comment->user_id) {
                continue;
            }

            $user = User::find($follower->user_id);
            if ($user) {
                $user->notify(new \App\Notifications\ProductCommentNotification($product, $comment));
            }
        }

        // Also notify the product owner if they're not the commenter
        if ($product->user_id != $comment->user_id) {
            $product->user->notify(new \App\Notifications\ProductCommentNotification($product, $comment));
        }
    }

    /**
     * Notify the parent comment author when someone replies to their comment
     */
    private function notifyCommentReply(ProductComment $parentComment, ProductComment $reply)
    {
        // Don't notify if the replier is the same as the parent comment author
        if ($parentComment->user_id == $reply->user_id) {
            return;
        }

        $parentComment->user->notify(
            new \App\Notifications\ProductCommentReplyNotification(
                $parentComment->product,
                $parentComment,
                $reply
            )
        );
    }

    /**
     * Display report form for a product
     */
    public function reportProduct($productId)
    {
        if (!Auth::check()) {
            return redirect()->route('web.auth.login');
        }

        $product = Product::findOrFail($productId);

        return view('web.report_product', compact('product'));
    }

    /**
     * Submit a report for a product
     */
    public function submitReport(Request $request, $productId)
    {
        if (!Auth::check()) {
            return redirect()->route('web.auth.login');
        }

        $validated = $request->validate([
            'reason' => 'required|string|max:255',
            'details' => 'required|string|max:1000',
        ]);

        $report = new Report();
        $report->model_id = $productId;
        $report->type ='product';
        $report->reporter_id = Auth::id();
        $report->text =$validated['reason'] .' - '. $validated['details'];
        $report->save();

        return redirect()->route('web.product', Product::findOrFail($productId)->slug)
            ->with('success', 'تم الارسال بنجاح');
    }

    /**
     * Store a general report (for chat messages, etc.)
     */
    public function storeReport(Request $request)
    {
        if (!Auth::check()) {
            return redirect()->route('web.auth.login');
        }

        $validated = $request->validate([
            'type' => 'required|string|in:product,comment,user,chat_message',
            'model_id' => 'required|integer',
            'text' => 'required|string|max:1000',
        ]);

        $report = new Report();
        $report->type = $validated['type'];
        $report->model_id = $validated['model_id'];
        $report->reporter_id = Auth::id();
        $report->text = $validated['text'];
        $report->save();

        return redirect()->back()->with('success', __('تم إرسال البلاغ بنجاح'));
    }

    /**
     * Subscribe to newsletter
     */
    public function subscribeNewsletter(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email|max:255',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator, 'newsletter')
                ->with('newsletter_error', 'البريد الإلكتروني غير صالح');
        }

        // Check if email already exists
        $existingSubscriber = NewsletterSubscriber::where('email', $request->email)->first();

        if ($existingSubscriber) {
            if ($existingSubscriber->is_active) {
                return redirect()->back()
                    ->with('newsletter_error', 'أنت مشترك بالفعل في النشرة البريدية');
            } else {
                // Reactivate subscription
                $existingSubscriber->is_active = true;
                $existingSubscriber->save();

                return redirect()->back()
                    ->with('newsletter_success', 'تم إعادة تفعيل اشتراكك في النشرة البريدية بنجاح');
            }
        }

        // Create new subscriber
        $subscriber = new NewsletterSubscriber();
        $subscriber->email = $request->email;
        $subscriber->locale = session()->get('locale', 'ar');
        $subscriber->save();

        return redirect()->back()
            ->with('newsletter_success', 'تم الاشتراك في النشرة البريدية بنجاح');
    }

    /**
     * Display user profile
     */
    public function profileShow()
    {
        if (!Auth::check()) {
            return redirect()->route('web.auth.login');
        }

        $user = Auth::user();

        // Load relationships for counts
        $user->load(['products', 'favorites', 'followers']);

        return view('web.profile.show', compact('user'));
    }

    /**
     * Display user profile edit form
     */
    public function profileEdit()
    {
        if (!Auth::check()) {
            return redirect()->route('web.auth.login');
        }

        $user = Auth::user();
        $user->load('sessions');

        return view('web.profile.edit', compact('user'));
    }

    /**
     * Update user profile
     */
    public function profileUpdate(Request $request)
    {
        if (!Auth::check()) {
            return redirect()->route('web.auth.login');
        }

        $user = Auth::user();

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email,' . $user->id,
            'phone' => 'nullable|string|max:20',
            'bio' => 'nullable|string|max:1000',
            'avatar' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'cover' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        $user->name = $validated['name'];
        $user->email = $validated['email'];
        $user->phone = $validated['phone'] ?? null;



        // Update or create user session
        $userSession = $user->sessions->first() ?? new \App\Models\UserSession();
        $userSession->user_id = $user->id;
        $userSession->bio = $validated['bio'] ?? null;

        // Handle avatar upload
        if ($request->hasFile('avatar')) {
            $avatar = $request->file('avatar');
            $filename = time() . '_avatar.' . $avatar->getClientOriginalExtension();
            $avatar->storeAs('avatars', $filename, 'public');
            $userSession->avatar = $filename;
        }

        // Handle cover upload
        if ($request->hasFile('cover')) {
            $cover = $request->file('cover');
            $filename = time() . '_cover.' . $cover->getClientOriginalExtension();
            $cover->storeAs('covers', $filename, 'public');
            $user->cover = 'covers/' . $filename;
        }

        $user->save();
        $userSession->save();

        return redirect()->route('web.profile.show')->with('success', __('تم تحديث الملف الشخصي بنجاح!'));
    }

    /**
     * Display user products
     */
    public function profileProducts()
    {
        if (!Auth::check()) {
            return redirect()->route('web.auth.login');
        }

        $user = Auth::user();
        $products = $user->products()->with(['category', 'media', 'commission'])->orderBy('created_at', 'desc')->paginate(12);

        return view('web.profile.products', compact('products'));
    }

    /**
     * Display user favorites
     */
    public function profileFavorites()
    {
        if (!Auth::check()) {
            return redirect()->route('web.auth.login');
        }

        $user = Auth::user();
        $favorites = $user->favorites()->with(['category', 'media'])->orderBy('created_at', 'desc')->paginate(12);

        return view('web.profile.favorites', compact('favorites'));
    }

    /**
     * Display user followers
     */
    public function profileFollowers()
    {
        if (!Auth::check()) {
            return redirect()->route('web.auth.login');
        }

        $user = Auth::user();
        $followers = $user->followers()->with('user')->orderBy('created_at', 'desc')->paginate(12);

        return view('web.profile.followers', compact('followers'));
    }

    /**
     * Display users that the current user is following
     */
    public function profileFollowing()
    {
        if (!Auth::check()) {
            return redirect()->route('web.auth.login');
        }

        $user = Auth::user();
        $following = $user->follows()->where('followable_type', User::class)->with('followable')->orderBy('created_at', 'desc')->paginate(12);

        return view('web.profile.following', compact('following'));
    }

    /**
     * Display products that the current user is following
     */
    public function profileFollowedProducts()
    {
        if (!Auth::check()) {
            return redirect()->route('web.auth.login');
        }

        $user = Auth::user();
        $followedProducts = $user->follows()
            ->where('followable_type', Product::class)
            ->with(['followable' => function($query) {
                $query->with(['user', 'category', 'media']);
            }])
            ->orderBy('created_at', 'desc')
            ->paginate(12);

        return view('web.profile.followed_products', compact('followedProducts'));
    }

    /**
     * Toggle follow status for a user
     */
    public function toggleFollow(Request $request)
    {
        if (!Auth::check()) {
            return redirect()->route('web.auth.login');
        }

        $validated = $request->validate([
            'type' => 'required|string|in:user,product,category,tag,location,brand',
            'id' => 'required|string',
        ]);

        $user = Auth::user();
        $type = $validated['type'];
        $id = $validated['id'];

        switch ($type) {
            case 'user':
                $followable = User::where('hashId', $id)->firstOrFail();
                break;
            case 'product':
                $followable = Product::findOrFail($id);
                break;
            case 'category':
                $followable = Category::findOrFail($id);
                break;
            case 'tag':
                $followable = Tag::findOrFail($id);
                break;
            case 'location':
                $followable = Location::findOrFail($id);
                break;
            case 'brand':
                $followable = Brand::findOrFail($id);
                break;
            default:
                return back()->with('error', __('نوع غير صالح'));
        }

        $existing = Follow::where('user_id', $user->id)
            ->where('followable_type', get_class($followable))
            ->where('followable_id', $followable->id)
            ->first();

        if ($existing) {
            $existing->delete();
            $message = __('تم إلغاء المتابعة بنجاح');
        } else {
            Follow::create([
                'user_id' => $user->id,
                'followable_type' => get_class($followable),
                'followable_id' => $followable->id,
            ]);
            $message = __('تمت المتابعة بنجاح');
        }

        return back()->with('success', $message);
    }

    /**
     * Toggle favorite status for a product (profile version)
     */
    public function profileToggleFavorite($productId)
    {
        if (!Auth::check()) {
            return redirect()->route('web.auth.login');
        }

        $user = Auth::user();
        $product = Product::findOrFail($productId);

        if ($user->favorites()->where('product_id', $product->id)->exists()) {
            $user->favorites()->detach($product);
            $message = __('تم إزالة المنتج من المفضلة');
        } else {
            $user->favorites()->attach($product);
            $message = __('تمت إضافة المنتج إلى المفضلة');
        }

        return back()->with('success', $message);
    }

    /**
     * Edit a product
     */
    public function editProduct($id)
    {
        if (!Auth::check()) {
            return redirect()->route('web.auth.login');
        }

        // Check if email is verified
        if (!Auth::user()->hasVerifiedEmail()) {
            return redirect()->route('web.auth.verification.notice');
        }

        $user = Auth::user();
        $product = Product::where('id', $id)
            ->where('user_id', $user->id)
            ->with(['category', 'location', 'tags', 'media'])
            ->firstOrFail();

        $categories = Category::whereNull('parent_id')->with('children')->get();
        $locations = Location::orderBy('name')->get();

        return view('web.profile.edit_product', compact('product', 'categories', 'locations'));
    }

    /**
     * Delete a product
     */
    public function deleteProduct($id)
    {
        if (!Auth::check()) {
            return redirect()->route('web.auth.login');
        }

        $user = Auth::user();
        $product = Product::where('id', $id)
            ->where('user_id', $user->id)
            ->firstOrFail();

        // Delete product
        $product->deleted_by = $user->id;
        $product->save();
        $product->delete();

        return redirect()->route('web.profile.products')->with('success', __('تم حذف الإعلان بنجاح'));
    }

    /**
     * Update a product
     */
    public function updateProduct(Request $request, $id)
    {
        if (!Auth::check()) {
            return redirect()->route('web.auth.login');
        }

        // Check if email is verified
        if (!Auth::user()->hasVerifiedEmail()) {
            return redirect()->route('web.auth.verification.notice');
        }

        $user = Auth::user();
        $product = Product::where('id', $id)
            ->where('user_id', $user->id)
            ->firstOrFail();

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'desc' => 'required|string',
            'price' => 'nullable|numeric|min:0',
            'category_id' => 'required|exists:categories,id',
            'location_id' => 'required|exists:locations,id',
            'condition' => 'required|string',
            'images.*' => 'image|mimes:jpeg,png,jpg,gif|max:2048',
            'delete_media.*' => 'nullable|exists:product_media,id',
        ]);

        $product->title = $validated['title'];
        $product->desc = $validated['desc'];
        $product->price = $validated['price'];
        $product->category_id = $validated['category_id'];
        $product->location_id = $validated['location_id'];
        $product->condition = $validated['condition'];
        $product->allow_phone_call = $request->has('allow_phone_call');
        $product->allow_chat = $request->has('allow_chat');
        $product->alternative_phone = $request->input('alternative_phone');
        $product->save();

        // Handle image deletions
        if ($request->has('delete_media')) {
            foreach ($request->input('delete_media') as $mediaId) {
                $media = ProductMedia::where('id', $mediaId)
                    ->where('product_id', $product->id)
                    ->first();

                if ($media) {
                    // Delete file from storage
                    Storage::disk('products')->delete($media->path);
                    $media->delete();
                }
            }
        }

        // Handle new image uploads
        if ($request->hasFile('images')) {
            foreach ($request->file('images') as $index => $image) {
                $fileOriginalName = $image->getClientOriginalName();
                $fileSize = $image->getSize();
                $fileMimeType = $image->getClientMimeType();
                $fileExtension = $image->getClientOriginalExtension();
                $fileName = time() . '_' . $index . '.' . $fileExtension;

                // Store in product-specific directory
                $path = "products/{$product->id}/images/" . $fileName;
                Storage::disk('products')->put($path, file_get_contents($image));

                $media = new ProductMedia();
                $media->name = $fileName;
                $media->type = 'image';
                $media->mime_type = $fileMimeType;
                $media->file_size = $fileSize;
                $media->extension = $fileExtension;
                $media->path = $path;
                $media->order = $index;
                $media->is_primary = $index === 0 && !$product->media()->where('is_primary', true)->exists();
                $media->product_id = $product->id;
                $media->save();

                // Dispatch job to process the media
                ProcessProductMedia::dispatch($media, $path);
            }
        }

        return redirect()->route('web.product', $product->slug)->with('success', __('تم تحديث الإعلان بنجاح'));
    }

    /**
     * Mark a product as sold
     */
    public function markProductSold($id)
    {
        if (!Auth::check()) {
            return redirect()->route('web.auth.login');
        }

        $user = Auth::user();
        $product = Product::where('id', $id)
            ->where('user_id', $user->id)
            ->firstOrFail();

        $product->status = 'sold';
        $product->save();

        return redirect()->route('web.profile.products')->with('success', __('تم تحديث حالة الإعلان إلى "تم البيع" بنجاح'));
    }

    /**
     * Mark a product as deleted (status change, not actual deletion)
     */
    public function markProductDeleted($id)
    {
        if (!Auth::check()) {
            return redirect()->route('web.auth.login');
        }

        $user = Auth::user();
        $product = Product::where('id', $id)
            ->where('user_id', $user->id)
            ->firstOrFail();

        $product->status = 'deleted';
        $product->save();

        return redirect()->route('web.profile.products')->with('success', __('تم تحديث حالة الإعلان إلى "محذوف" بنجاح'));
    }

    /**
     * Create a commission for a product
     */
    public function createCommission(Request $request, $productId)
    {
        if (!Auth::check()) {
            return response()->json(['success' => false, 'message' => 'يجب تسجيل الدخول أولاً'], 401);
        }

        $user = Auth::user();
        $product = Product::where('id', $productId)
            ->where('user_id', $user->id)
            ->firstOrFail();

        // Check if commission already exists for this product
        $existingCommission = Commission::where('user_id', $user->id)
            ->where('product_id', $product->id)
            ->first();

        if ($existingCommission) {
            return response()->json([
                'success' => false,
                'message' => 'يوجد بالفعل طلب عمولة لهذا المنتج'
            ], 400);
        }

        $validated = $request->validate([
            'sell_price' => 'required|numeric|min:0',
            'payment_method' => 'required|in:' . implode(',', PaymentMethodEnum::getValues()),
            'payment_proof' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'notes' => 'nullable|string|max:500',
        ]);

        $paymentMethod = PaymentMethodEnum::from($validated['payment_method']);

        // Validate payment proof for methods that require it
        if (PaymentMethodEnum::requiresProof($paymentMethod->value) && !$request->hasFile('payment_proof')) {
            return response()->json([
                'success' => false,
                'message' => 'إثبات الدفع مطلوب لهذه الطريقة'
            ], 400);
        }

        try {
            // Update product price with sell price
            $product->price = $validated['sell_price'];
            $product->save();

            $commissionService = new CommissionService();
            $commission = $commissionService->createCommission(
                $user,
                $product,
                $paymentMethod,
                $request->file('payment_proof'),
                $validated['notes']
            );

            return response()->json([
                'success' => true,
                'message' => 'تم تقديم طلب دفع العمولة بنجاح'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء معالجة الطلب: ' . $e->getMessage()
            ], 500);
        }
    }
}
