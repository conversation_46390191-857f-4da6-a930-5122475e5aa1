@extends('web.layouts.layout')
@section('title', __('الملف الشخصي'))

@section('page-meta')
    <meta name="description" content="{{ __('الملف الشخصي في موقع حراجي') }}">
    <meta name="keywords" content="{{ __('حراجي, ملف شخصي, إعلانات, بيع, شراء') }}">
@endsection

@section('content')
    <main class="py-8">
        <div class="container mx-auto px-4">
            <!-- Breadcrumbs -->
            <div class="text-sm breadcrumbs mb-6">
                <ul>
                    <li><a href="{{ route('web.index') }}">{{ __('الرئيسية') }}</a></li>
                    <li>{{ __('الملف الشخصي') }}</li>
                </ul>
            </div>

            <!-- Main Split Layout -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                <!-- Left Half - Profile Information -->
                <div class="space-y-6">
                    <!-- Profile Header Card -->
                    <div class="bg-base-100 rounded-xl shadow-lg overflow-hidden">
                        <!-- Cover Photo -->
                        <div class="relative h-32 bg-gradient-to-r from-primary/20 to-secondary/20">
                            @if ($user->cover)
                                <img src="{{ asset('storage/' . $user->cover) }}" alt="{{ $user->name }}"
                                    class="w-full h-full object-cover">
                            @else
                                <div
                                    class="w-full h-full bg-gradient-to-r from-primary/20 to-secondary/20 flex items-center justify-center">
                                    <i class="fas fa-image text-3xl text-base-content/30"></i>
                                </div>
                            @endif

                            <!-- Edit Profile Button -->
                            <a href="{{ route('web.profile.edit') }}"
                                class="absolute top-3 right-3 rtl:right-auto rtl:left-3 btn btn-sm btn-primary">
                                <i class="fas fa-edit mr-1 rtl:ml-1 rtl:mr-0"></i> {{ __('تعديل') }}
                            </a>
                        </div>

                        <!-- Profile Info -->
                        <div class="relative px-6 pt-12 pb-6">
                            <!-- Avatar -->
                            <div class="absolute -top-8 left-6 rtl:left-auto rtl:right-6">
                                <div class="avatar">
                                    <div
                                        class="w-16 h-16 rounded-full ring ring-primary ring-offset-base-100 ring-offset-2">
                                        @if ($user->sessions->count() > 0 && $user->avatar())
                                            <img src="{{ $user->avatar() }}" alt="{{ $user->name }}">
                                        @else
                                            <img src="{{ asset('assets/default-avatar.png') }}" alt="{{ $user->name }}">
                                        @endif
                                    </div>
                                </div>
                            </div>

                            <!-- User Info -->
                            <div class="mb-4">
                                <h1 class="text-xl font-bold mb-1">{{ $user->name }}</h1>
                                <p class="text-sm text-base-content/70">{{ __('عضو منذ') }}
                                    {{ $user->created_at->format('Y-m-d') }}</p>

                                <!-- Rating Display -->
                                @if ($user->ratings_count > 0)
                                    <div class="flex items-center gap-2 mt-2">
                                        <div class="rating rating-sm">
                                            @for ($i = 1; $i <= 5; $i++)
                                                <input type="radio" class="mask mask-star-2 bg-orange-400" disabled
                                                    {{ $i <= round($user->average_rating) ? 'checked' : '' }} />
                                            @endfor
                                        </div>
                                        <span class="text-sm text-base-content/70">({{ $user->ratings_count }}
                                            {{ __('تقييم') }})</span>
                                    </div>
                                @endif
                            </div>

                            <!-- Status Badges -->
                            <div class="flex flex-wrap gap-2 mb-4">
                                @if ($user->email_verified_at)
                                    <div class="badge badge-success badge-sm gap-1">
                                        <i class="fas fa-check-circle text-xs"></i> {{ __('مؤكد') }}
                                    </div>
                                @else
                                    <div class="badge badge-warning badge-sm gap-1">
                                        <i class="fas fa-exclamation-circle text-xs"></i> {{ __('غير مؤكد') }}
                                    </div>
                                @endif

                                <!-- Verification Status -->
                                @if ($user->isVerified())
                                    <div class="badge badge-primary gap-2">
                                        <i class="fas fa-shield-check"></i> {{ __('حساب موثق') }}
                                    </div>
                                @elseif($user->hasPendingVerification())
                                    <div class="badge badge-info gap-2">
                                        <i class="fas fa-clock"></i> {{ __('طلب التوثيق قيد المراجعة') }}
                                    </div>
                                @else
                                    <div class="badge badge-ghost gap-2">
                                        <i class="fas fa-shield-alt"></i> {{ __('غير موثق') }}
                                    </div>
                                @endif
                            </div>

                            <!-- Contact Info -->
                            <div class="space-y-2 text-sm">
                                <div class="flex items-center gap-2">
                                    <i class="fas fa-envelope text-primary text-xs"></i>
                                    <span>{{ $user->email }}</span>
                                </div>
                                @if ($user->phone)
                                    <div class="flex items-center gap-2">
                                        <i class="fas fa-phone-alt text-primary text-xs"></i>
                                        <span>{{ $user->phone }}</span>
                                    </div>
                                @endif
                            </div>

                            <!-- Bio -->
                            @if ($user->sessions->count() > 0 && $user->sessions->first()->bio)
                                <div class="mt-4 pt-4 border-t border-base-300">
                                    <h3 class="font-semibold mb-2 text-sm">{{ __('نبذة') }}</h3>
                                    <p class="text-sm text-base-content/80">{{ $user->sessions->first()->bio }}</p>
                                </div>
                            @endif

                            <!-- Verification Request Button -->
                            @if (!$user->isVerified() && !$user->hasPendingVerification() && $user->email_verified_at)
                                <div class="mt-4 pt-4 border-t border-base-300">
                                    <a href="{{ route('web.verification.form') }}"
                                        class="btn btn-outline btn-primary btn-sm w-full gap-2">
                                        <i class="fas fa-shield-check"></i>
                                        {{ __('طلب التوثيق') }}
                                    </a>
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- Statistics Card -->
                    <div class="bg-base-100 rounded-xl shadow-lg p-6">
                        <h2 class="text-lg font-bold mb-4">{{ __('الإحصائيات') }}</h2>
                        <div class="grid grid-cols-2 gap-4">
                            <a href="{{ route('web.profile.products') }}" class="stat-card">
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-primary">{{ $user->products->count() }}</div>
                                    <div class="text-xs text-base-content/70">{{ __('إعلاناتي') }}</div>
                                </div>
                            </a>

                            <a href="{{ route('web.profile.favorites') }}" class="stat-card">
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-primary">{{ $user->favorites->count() }}</div>
                                    <div class="text-xs text-base-content/70">{{ __('المفضلة') }}</div>
                                </div>
                            </a>

                            <a href="{{ route('web.profile.followers') }}" class="stat-card">
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-primary">{{ $user->followers->count() }}</div>
                                    <div class="text-xs text-base-content/70">{{ __('المتابعين') }}</div>
                                </div>
                            </a>

                            <a href="{{ route('web.profile.following') }}" class="stat-card">
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-primary">
                                        {{ $user->follows()->where('followable_type', 'App\\Models\\User')->count() }}
                                    </div>
                                    <div class="text-xs text-base-content/70">{{ __('أتابعهم') }}</div>
                                </div>
                            </a>
                        </div>
                    </div>

                    <!-- Achievements Card -->
                    @if ($user->achievements->count() > 0)
                        <div class="bg-base-100 rounded-xl shadow-lg p-6">
                            <h2 class="text-lg font-bold mb-4">{{ __('الإنجازات') }}</h2>
                            <div class="grid grid-cols-2 sm:grid-cols-3 gap-3">
                                @foreach ($user->achievements->take(6) as $achievement)
                                    <div class="achievement-badge" title="{{ $achievement->description }}">
                                        <div
                                            class="text-center p-3 bg-base-200 rounded-lg hover:bg-primary/10 transition-colors">
                                            @if ($achievement->icon)
                                                <i class="{{ $achievement->icon }} text-2xl text-primary mb-2"></i>
                                            @else
                                                <i class="fas fa-trophy text-2xl text-primary mb-2"></i>
                                            @endif
                                            <div class="text-xs font-medium">{{ $achievement->name }}</div>
                                            <div class="text-xs text-base-content/60">
                                                {{ $achievement->pivot->achieved_at }}</div>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                            @if ($user->achievements->count() > 6)
                                <div class="text-center mt-4">
                                    <button class="btn btn-sm btn-outline">{{ __('عرض المزيد') }}
                                        ({{ $user->achievements->count() - 6 }})</button>
                                </div>
                            @endif
                        </div>
                    @endif
                </div>

                <!-- Right Half - User Products -->
                <div class="space-y-6">
                    <div class="bg-base-100 rounded-xl shadow-lg overflow-hidden">
                        <div class="p-6 border-b border-base-300">
                            <div class="flex justify-between items-center">
                                <h2 class="text-lg font-bold">{{ __('إعلاناتي') }}</h2>
                                <a href="{{ route('web.profile.products') }}" class="btn btn-sm btn-outline btn-primary">
                                    {{ __('عرض الكل') }}
                                </a>
                            </div>
                        </div>

                        <!-- Products Container with Max Height -->
                        <div class="max-h-96 overflow-y-auto">
                            @if ($user->products->count() > 0)
                                <div class="p-4">
                                    @livewire('products', [
                                        'pageType' => 'profile',
                                        'filters' => [
                                            'sort' => 'newest',
                                        ],
                                        'showCategories' => false,
                                        'showFilters' => false,
                                        'perPage' => 6,
                                    ])
                                </div>
                            @else
                                <div class="text-center py-12">
                                    <i class="fas fa-tag text-4xl text-base-content/30 mb-4"></i>
                                    <p class="text-base mb-4">{{ __('لا توجد إعلانات حتى الآن') }}</p>
                                    @if ($user->hasVerifiedEmail())
                                        <a href="{{ route('web.products.create') }}" class="btn btn-primary btn-sm">
                                            <i class="fas fa-plus mr-2 rtl:ml-2 rtl:mr-0"></i>
                                            {{ __('إضافة إعلان جديد') }}
                                        </a>
                                    @else
                                        <a href="{{ route('web.auth.verification.notice') }}"
                                            class="btn btn-warning btn-sm">
                                            <i class="fas fa-exclamation-triangle mr-2 rtl:ml-2 rtl:mr-0"></i>
                                            {{ __('تحقق من البريد') }}
                                        </a>
                                    @endif
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            <!-- Bottom Full Width Sections -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Notifications Section -->
                <div class="bg-base-100 rounded-xl shadow-lg overflow-hidden">
                    <div class="p-6 border-b border-base-300">
                        <h2 class="text-lg font-bold">{{ __('الإشعارات') }}</h2>
                    </div>

                    <div class="max-h-80 overflow-y-auto">
                        @if ($user->userNotifications->count() > 0)
                            <div class="divide-y divide-base-300">
                                @foreach ($user->userNotifications as $notification)
                                    @php
                                        $userNotification = $notification->pivot;
                                        $isRead = $userNotification && $userNotification->read_at;
                                    @endphp
                                    <div
                                        class="p-4 hover:bg-base-200 transition-colors {{ !$isRead ? 'bg-primary/5' : '' }}">
                                        <div class="flex items-start gap-3">
                                            <div class="flex-shrink-0">
                                                <div
                                                    class="w-2 h-2 rounded-full {{ !$isRead ? 'bg-primary' : 'bg-base-300' }}">
                                                </div>
                                            </div>
                                            <div class="flex-1 min-w-0">
                                                <h4
                                                    class="font-medium text-sm {{ !$isRead ? 'text-base-content' : 'text-base-content/70' }}">
                                                    {{ $notification->title }}
                                                </h4>
                                                <p class="text-sm text-base-content/70 mt-1">{{ $notification->body }}</p>
                                                <p class="text-xs text-base-content/50 mt-2">
                                                    {{ $notification->created_at->diffForHumans() }}</p>
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        @else
                            <div class="text-center py-12">
                                <i class="fas fa-bell text-4xl text-base-content/30 mb-4"></i>
                                <p class="text-base">{{ __('لا توجد إشعارات') }}</p>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Messages Section -->
                <div class="bg-base-100 rounded-xl shadow-lg overflow-hidden">
                    <div class="p-6 border-b border-base-300">
                        <div class="flex justify-between items-center">
                            <h2 class="text-lg font-bold">{{ __('المحادثات الأخيرة') }}</h2>
                            <a href="{{ route('web.chat.index') }}" class="btn btn-sm btn-outline btn-primary">
                                {{ __('عرض الكل') }}
                            </a>
                        </div>
                    </div>

                    <div class="max-h-80 overflow-y-auto">
                        @php
                            $recentChats = \Modules\Chat\app\Models\Chat::where('sender_id', $user->id)
                                ->orWhere('receiver_id', $user->id)
                                ->with(['sender', 'receiver', 'lastMessage'])
                                ->orderBy('last_message_at', 'desc')
                                ->limit(4)
                                ->get();
                        @endphp

                        @if ($recentChats->count() > 0)
                            <div class="divide-y divide-base-300">
                                @foreach ($recentChats as $chat)
                                    @php
                                        $otherUser = $chat->sender_id == $user->id ? $chat->receiver : $chat->sender;
                                        $unreadCount = $chat->messages
                                            ->where('user_id', '!=', $user->id)
                                            ->where('is_read', false)
                                            ->count();
                                    @endphp
                                    <a href="{{ route('web.chat.show', $chat->id) }}"
                                        class="flex items-center p-4 hover:bg-base-200 transition-colors">
                                        <div class="avatar">
                                            <div class="w-10 h-10 rounded-full">
                                                <img src="{{ $otherUser->avatar() }}" alt="{{ $otherUser->name }}">
                                            </div>
                                        </div>
                                        <div class="ml-3 rtl:mr-3 rtl:ml-0 flex-1 min-w-0">
                                            <div class="flex justify-between items-center">
                                                <h3 class="font-medium text-sm truncate">{{ $otherUser->name }}</h3>
                                                <span
                                                    class="text-xs text-base-content/50">{{ $chat->last_message_at ? $chat->last_message_at->diffForHumans() : $chat->created_at->diffForHumans() }}</span>
                                            </div>
                                            <p class="text-sm text-base-content/70 truncate">
                                                @if ($chat->lastMessage)
                                                    {{ $chat->lastMessage->content }}
                                                @else
                                                    <span class="italic">{{ __('لا توجد رسائل') }}</span>
                                                @endif
                                            </p>
                                            @if ($unreadCount > 0)
                                                <div class="mt-1">
                                                    <span class="badge badge-primary badge-xs">{{ $unreadCount }}</span>
                                                </div>
                                            @endif
                                        </div>
                                    </a>
                                @endforeach
                            </div>
                        @else
                            <div class="text-center py-12">
                                <i class="far fa-comments text-4xl text-base-content/30 mb-4"></i>
                                <p class="text-base">{{ __('لا توجد محادثات حتى الآن') }}</p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </main>
@endsection

@push('styles')
    <style>
        .stat-card {
            background-color: hsl(var(--b2));
            border-radius: 0.5rem;
            padding: 1rem;
            text-align: center;
            transition: all 0.3s ease;
            border: 1px solid hsl(var(--b3));
        }

        .stat-card:hover {
            background-color: hsl(var(--p) / 0.1);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px hsl(var(--b3));
        }

        .achievement-badge:hover {
            transform: scale(1.05);
            transition: transform 0.2s ease;
        }

        /* Custom scrollbar for better UX */
        .max-h-96::-webkit-scrollbar,
        .max-h-80::-webkit-scrollbar {
            width: 6px;
        }

        .max-h-96::-webkit-scrollbar-track,
        .max-h-80::-webkit-scrollbar-track {
            background: hsl(var(--b2));
            border-radius: 3px;
        }

        .max-h-96::-webkit-scrollbar-thumb,
        .max-h-80::-webkit-scrollbar-thumb {
            background: hsl(var(--b3));
            border-radius: 3px;
        }

        .max-h-96::-webkit-scrollbar-thumb:hover,
        .max-h-80::-webkit-scrollbar-thumb:hover {
            background: hsl(var(--bc) / 0.3);
        }
    </style>
@endpush
