<?php

use App\Events\NotificationsEvent;
use App\Http\Controllers\AppNotificationController;
use App\Http\Controllers\AppUpdateController;
use App\Http\Controllers\CategoryController;
use App\Http\Controllers\FaqsController;
use App\Http\Controllers\FileManagerController;
use App\Http\Controllers\language\LanguageController;
use App\Http\Controllers\pages\HomeController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\RoleController;
use App\Http\Controllers\SettingsController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\ShortUrlController;
use App\Http\Controllers\PageController;
use App\Http\Controllers\QueueController;
use Illuminate\Support\Facades\Route;
use Modules\TrafficLogs\controller\TrafficLogsController;
use Modules\Blog\Controller\BlogCategoryController;
use Modules\Blog\Controller\BlogController;
use App\Http\Controllers\ProductController;
use App\Http\Controllers\LocationController;
use App\Http\Controllers\TagController;
use App\Http\Controllers\BrandController;
use App\Http\Controllers\AchievementController;
use Http\Controllers\AchievementsController;
use App\Http\Controllers\BannerAdController;
use App\Http\Controllers\CategoryAttributeController;
use App\Http\Controllers\ReportController;
use App\Http\Controllers\UserRateController;
use Modules\UserTracker\app\Http\Middleware\TrackUserActivity;
use App\Http\Controllers\AppMenuController;
use App\Http\Controllers\front\Front2Controller;
use App\Http\Controllers\front\Front2AuthController;
use App\Http\Middleware\TrackUserSession;
use Spatie\Sitemap\Sitemap;
use Spatie\Sitemap\Tags\Url;

Route::get('/lang/{locale}', [LanguageController::class, 'swap']);





    // pages




    Route::get('page/{slug}', [PageController::class, 'open'])->name('page');



    Route::prefix('admin')
    ->middleware(['auth', TrackUserActivity::class])
    ->group(function () {
        Route::get('/deleteAllFiles', [HomeController::class, 'deleteAllFiles'])->name('deleteAllFiles');
        Route::get('/homeCreateBook', [HomeController::class, 'testCreateBook'])->name('homeCreateBook');
        Route::get('/dashboard', [HomeController::class, 'index'])->name('dashboard');

        Route::resource('users', UserController::class);
        Route::post('users/{user}', [UserController::class, 'sendNotificationToUser'])->name('users.send.notification');
        Route::resource('roles', RoleController::class);
        Route::resource('categories', CategoryController::class);
        Route::resource('app-updates', AppUpdateController::class);
        Route::resource('app-notifications', AppNotificationController::class);
        Route::resource('pages', PageController::class);
        Route::resource('faqs', FaqsController::class);

        // File Manager Routes
        Route::prefix('files')
            ->name('files.')
            ->group(function () {
                Route::get('/search', [FileManagerController::class, 'search'])->name('search');
                Route::get('/{path?}', [FileManagerController::class, 'index'])
                    ->name('index')
                    ->where('path', '.*');

                Route::post('/store', [FileManagerController::class, 'store'])->name('store');
                Route::post('/store2', [FileManagerController::class, 'store2'])->name('store2');
                Route::delete('/destroy', [FileManagerController::class, 'destroy'])->name('destroy');
                Route::post('/bulk-delete', [FileManagerController::class, 'bulkDelete'])->name('bulk-delete');
                Route::post('/download', [FileManagerController::class, 'download'])->name('download');
                Route::post('/download-directory', [FileManagerController::class, 'downloadDirectory'])->name('download-directory');
                Route::post('/file-details', [FileManagerController::class, 'getFileDetails'])->name('file-details');
                Route::post('/rename', [FileManagerController::class, 'rename'])->name('rename');
                Route::post('/paste/{filename}', [FileManagerController::class, 'paste'])->name('paste');
            });

        Route::prefix('file-manager')
            ->controller(FileManagerController::class)
            ->group(function () {
                Route::get('/files/{path?}', 'index')->where('path', '.*')->name('files.index');
                Route::post('/files/store/{disk?}', 'store')->name('files.store');
                Route::post('/files', 'store2')->name('files.store2');
                // Route::delete('/files/{filename}', 'destroy')->name('files.destroy');
                Route::post('/download/file', 'download')->name('files.download');
            });

        // Marketplace routes
        Route::resource('products', ProductController::class);

        Route::resource('locations', LocationController::class);
        Route::resource('tags', TagController::class);
        Route::resource('brands', BrandController::class);
        Route::resource('achievements', AchievementController::class);

        Route::get('/page/{slug}', [PageController::class, 'open'])->name('page');

        Route::resource('settings', SettingsController::class);

        Route::get('/traffic', [TrafficLogsController::class, 'index'])->name('traffic.index');
        Route::get('/traffic/logs/{id}', [TrafficLogsController::class, 'logs'])->name('traffic.logs');

        Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
        Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
        Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
        Route::get('/profile/notifications', [ProfileController::class, 'notifications'])->name('profile.notifications');
        Route::delete('/profile/notifications/{notification}', [ProfileController::class, 'deleteNotification'])->name('profile.notifications.delete');


        //* Short Urls
        Route::get('/short-urls', [ShortUrlController::class, 'index'])->name('short-urls.index');
        Route::post('/short-urls', [ShortUrlController::class, 'create'])->name('short-urls.store');
        Route::put('/short-urls/{shortUrl}', [ShortUrlController::class, 'update'])->name('short-urls.update');
        Route::delete('/ /{shortUrl}', [ShortUrlController::class, 'destroy'])->name('short-urls.destroy');

        //*Blogs
        Route::resource('blog-categories', BlogCategoryController::class);
        Route::resource('blogs', BlogController::class);
        Route::get('/blog/{slug}', [BlogController::class, 'show'])->name('blog');

        //* Queue

        Route::get('/queue/cancel/{id}', [QueueController::class, 'cancelJob'])->name('queue.cancel');
        Route::get('/queue/retry-all', [QueueController::class, 'retryAllFailedJobs']);
        Route::get('/queue/retry/{id}', [QueueController::class, 'retryFailedJob'])->name('queue.retry');
        Route::get('/queue/failed-jobs', [QueueController::class, 'listFailedJobs']);
        Route::get('/queue/flush', [QueueController::class, 'flushFailedJobs']);

        // Category Attributes routes
        Route::prefix('admin/categories/{category}')->name('categories.')->group(function () {
            Route::get('/attributes', [CategoryAttributeController::class, 'index'])->name('attributes.index');
            Route::get('/attributes/create', [CategoryAttributeController::class, 'create'])->name('attributes.create');
            Route::post('/attributes', [CategoryAttributeController::class, 'store'])->name('attributes.store');
            Route::get('/attributes/{attribute}/edit', [CategoryAttributeController::class, 'edit'])->name('attributes.edit');
            Route::put('/attributes/{attribute}', [CategoryAttributeController::class, 'update'])->name('attributes.update');
            Route::delete('/attributes/{attribute}', [CategoryAttributeController::class, 'destroy'])->name('attributes.destroy');
            Route::post('/attributes/update-order', [CategoryAttributeController::class, 'updateOrder'])->name('attributes.update-order');
        });

        // Reports
        Route::resource('reports', ReportController::class);

        Route::resource('app-menus', AppMenuController::class);

        // Banner Ads
        Route::resource('banner-ads', BannerAdController::class);

        // User Rates
        Route::resource('user-rates', UserRateController::class)->except(['create', 'edit', 'update']);
        Route::patch('user-rates/{userRate}/status', [UserRateController::class, 'updateStatus'])->name('user-rates.update-status');
        Route::get('users/{user}/ratings', [UserRateController::class, 'userRatings'])->name('users.ratings');

        // API endpoints within admin
        Route::get('api/banner-ads/{screen}', [BannerAdController::class, 'getBannerAdsForScreen']);
    });

Route::get('/srt/{shortCode}', [ShortUrlController::class, 'redirect']);

Route::get('/robots.txt', function () {
    return response("User-agent: *\nDisallow: /", 200, ['Content-Type' => 'text/plain']);
})->name('robots.txt');


// Include front-end routes
require __DIR__ . '/front.php';

// Include admin routes
// require __DIR__ . '/admin.php';

require __DIR__ . '/auth.php';
Route::get('/dashboard/refresh', [HomeController::class, 'refreshDashboard'])->name('dashboard.refresh');

Route::get('/test-notification', function () {
    NotificationsEvent::dispatch('Hello World');
    return 'Notification sent';
});


Route::get('/robots.txt', function () {
    $sitemapUrl = config('app.url') . '/sitemap.xml';

    $content = <<<EOT
    User-agent: *
    Disallow: /admin
    Disallow: /telescope
    Disallow: /horizon
    Disallow: /log-viewer
    Disallow: /queue
    Disallow: /profile
    Disallow: /short-urls
    Disallow: /uploads
    Disallow: /srt
    Disallow: /email/verify
    Disallow: /Reset-Pass

    # Allow public routes
    Allow: /
    Allow: /categories
    Allow: /category
    Allow: /search
    Allow: /product
    Allow: /seller
    Allow: /about
    Allow: /contact
    Allow: /blog
    Allow: /page

    Sitemap: {$sitemapUrl}
    EOT;

    return response($content, 200, ['Content-Type' => 'text/plain']);
})->name('robots.txt');

Route::get('/sitemap.xml', function () {
    $sitemap = Sitemap::create()
        // Home page
        ->add(Url::create('/'))

        // Static pages
        ->add(Url::create('/about'))
        ->add(Url::create('/contact'))

        // Marketplace pages
        ->add(Url::create('/categories'))
        ->add(Url::create('/search'))

        // Blog
        ->add(Url::create('/blog'));

    // Add dynamic pages from database
    // Categories
    $categories = \App\Models\Category::where('status', true)->get();
    foreach ($categories as $category) {
        $sitemap->add(Url::create('/category/' . $category->slug));
    }

    // Static pages from database
    $pages = \App\Models\Page::where('status', true)->get();
    foreach ($pages as $page) {
        $sitemap->add(Url::create('/page/' . $page->slug));
    }

    // Blog posts
    if (class_exists('Modules\Blog\Models\Blog')) {
        $blogs = \Modules\Blog\Models\Blog::where('is_published', true)->get();
        foreach ($blogs as $blog) {
            $sitemap->add(Url::create('/blog/' . $blog->slug));
        }
    }

    // Products (limit to approved products, max 1000 for performance)
    $products = \App\Models\Product::where('status', \App\Enums\StatusEnum::APPROVED->value)
        ->orderBy('created_at', 'desc')
        ->limit(1000)
        ->get();
    foreach ($products as $product) {
        $sitemap->add(Url::create('/product/' . $product->slug));
    }

    // Seller profiles (only include users with approved products)
    $sellers = \App\Models\User::whereHas('products', function($query) {
        $query->where('status', \App\Enums\StatusEnum::APPROVED->value);
    })->limit(500)->get();

    foreach ($sellers as $seller) {
        $sitemap->add(Url::create('/seller/' . $seller->username));
    }

    return $sitemap->toResponse(request());
});