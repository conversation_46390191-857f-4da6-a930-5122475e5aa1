<?php

use App\Events\NotificationsEvent;
use App\Http\Controllers\AppNotificationController;
use App\Http\Controllers\AppUpdateController;
use App\Http\Controllers\CategoryController;
use App\Http\Controllers\FaqsController;
use App\Http\Controllers\FileManagerController;
use App\Http\Controllers\language\LanguageController;
use App\Http\Controllers\pages\HomeController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\RoleController;
use App\Http\Controllers\SettingsController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\ShortUrlController;
use App\Http\Controllers\PageController;
use App\Http\Controllers\QueueController;
use App\Http\Controllers\ProductController;
use App\Http\Controllers\LocationController;
use App\Http\Controllers\TagController;
use App\Http\Controllers\BrandController;
use App\Http\Controllers\AchievementController;
use App\Http\Controllers\CommissionController;
use App\Http\Controllers\UserCommissionController;
use App\Http\Controllers\BannerAdController;
use App\Http\Controllers\CategoryAttributeController;
use App\Http\Controllers\ReportController;
use App\Http\Controllers\UserRateController;
use App\Http\Controllers\VerificationController;
use Illuminate\Support\Facades\Route;
use Modules\UserTracker\app\Http\Middleware\TrackUserActivity;
use Modules\TrafficLogs\controller\TrafficLogsController;
use Modules\Blog\Controller\BlogCategoryController;
use Modules\Blog\Controller\BlogController;

/*
|--------------------------------------------------------------------------
| Admin Routes
|--------------------------------------------------------------------------
|
| Here is where you can register admin routes for your application.
|
*/

Route::prefix('admin')
    ->middleware(['auth', TrackUserActivity::class])
    ->group(function () {
        Route::get('/deleteAllFiles', [HomeController::class, 'deleteAllFiles'])->name('deleteAllFiles');
        Route::get('/homeCreateBook', [HomeController::class, 'testCreateBook'])->name('homeCreateBook');
        Route::get('/', [HomeController::class, 'index'])->name('home');
        Route::get('/dashboard', [HomeController::class, 'dashboard'])->name('dashboard');
        Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
        Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
        Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
        Route::resource('users', UserController::class);
        Route::resource('roles', RoleController::class);
        Route::resource('app-notifications', AppNotificationController::class);
        Route::resource('pages', PageController::class);
        Route::resource('faqs', FaqsController::class);

        // File Manager Routes
        Route::prefix('files')
            ->name('files.')
            ->group(function () {
                Route::get('/search', [FileManagerController::class, 'search'])->name('search');
                Route::get('/', [FileManagerController::class, 'index'])->name('index');
                Route::get('/folder/{folder}', [FileManagerController::class, 'folder'])->name('folder');
                Route::post('/folder', [FileManagerController::class, 'createFolder'])->name('folder.create');
                Route::delete('/folder', [FileManagerController::class, 'deleteFolder'])->name('folder.delete');
                Route::post('/upload', [FileManagerController::class, 'upload'])->name('upload');
                Route::get('/file/{file}', [FileManagerController::class, 'file'])->name('file');
                Route::delete('/file', [FileManagerController::class, 'deleteFile'])->name('file.delete');
                Route::post('/move', [FileManagerController::class, 'move'])->name('move');
                Route::post('/rename', [FileManagerController::class, 'rename'])->name('rename');
                Route::post('/paste/{filename}', [FileManagerController::class, 'paste'])->name('paste');
            });

        // Settings Routes
        Route::get('/settings', [SettingsController::class, 'index'])->name('settings.index');
        Route::post('/settings', [SettingsController::class, 'store'])->name('settings.store');
        Route::get('/settings/clear-cache', [SettingsController::class, 'clearCache'])->name('settings.clear-cache');

        // App Update Routes
        Route::get('/app-update', [AppUpdateController::class, 'index'])->name('app-update.index');
        Route::post('/app-update', [AppUpdateController::class, 'update'])->name('app-update.update');

        // Short URL Routes
        Route::resource('short-urls', ShortUrlController::class);

        // Queue Management Routes
        Route::get('/queue', [QueueController::class, 'index'])->name('queue.index');
        Route::post('/queue/retry/{id}', [QueueController::class, 'retry'])->name('queue.retry');
        Route::post('/queue/forget/{id}', [QueueController::class, 'forget'])->name('queue.forget');
        Route::post('/queue/retry-all', [QueueController::class, 'retryAll'])->name('queue.retry-all');
        Route::post('/queue/forget-all', [QueueController::class, 'forgetAll'])->name('queue.forget-all');

        // Traffic Logs Routes
        Route::get('/traffic-logs', [TrafficLogsController::class, 'index'])->name('traffic-logs.index');
        Route::get('/traffic-logs/{id}', [TrafficLogsController::class, 'show'])->name('traffic-logs.show');

        // Blog Routes
        Route::resource('blog-categories', BlogCategoryController::class);
        Route::resource('blogs', BlogController::class);

        // Product Management Routes
        Route::resource('products', ProductController::class);
        Route::post('/products/{id}/status', [ProductController::class, 'updateStatus'])->name('products.status');
        Route::post('/products/{id}/featured', [ProductController::class, 'toggleFeatured'])->name('products.featured');
        Route::post('/products/{id}/verified', [ProductController::class, 'toggleVerified'])->name('products.verified');

        // Location Management Routes
        Route::resource('locations', LocationController::class);
        Route::get('/locations/{id}/children', [LocationController::class, 'children'])->name('locations.children');

        // Category Management Routes
        Route::resource('categories', CategoryController::class);
        Route::get('/categories/{id}/children', [CategoryController::class, 'children'])->name('categories.children');

        // Tag Management Routes
        Route::resource('tags', TagController::class);

        // Brand Management Routes
        Route::resource('brands', BrandController::class);

        // Achievement Management Routes
        Route::resource('achievements', AchievementController::class);

        // Commission Management Routes
        Route::resource('commissions', CommissionController::class);
        Route::resource('user-commissions', UserCommissionController::class);

        // Banner Ad Management Routes
        Route::resource('banner-ads', BannerAdController::class);

        // Category Attribute Management Routes
        Route::resource('category-attributes', CategoryAttributeController::class);

        // Report Management Routes
        Route::resource('reports', ReportController::class);

        // User Rate Management Routes
        Route::resource('user-rates', UserRateController::class);

        // App Menu Management Routes
        Route::resource('app-menus', AppMenuController::class);

        // Verification Routes
        Route::prefix('verifications')->name('verifications.')->group(function () {
            Route::get('/', [VerificationController::class, 'index'])->name('index');
            Route::get('/{verification}', [VerificationController::class, 'show'])->name('show');
            Route::post('/{verification}/approve', [VerificationController::class, 'approve'])->name('approve');
            Route::post('/{verification}/reject', [VerificationController::class, 'reject'])->name('reject');
            Route::post('/requirements', [VerificationController::class, 'storeRequirements'])->name('requirements.store');
            Route::get('/details/{verification}', [VerificationController::class, 'getVerificationDetails'])->name('details');
        });

        // API endpoints within admin
        Route::get('api/banner-ads/{screen}', [BannerAdController::class, 'getBannerAdsForScreen']);
    });

// Dashboard refresh route
Route::get('/dashboard/refresh', [HomeController::class, 'refreshDashboard'])->name('dashboard.refresh');
