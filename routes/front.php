<?php

use App\Http\Controllers\language\LanguageController;
use App\Http\Controllers\ShortUrlController;
use App\Http\Controllers\front\Front2Controller;
use App\Http\Controllers\front\Front2AuthController;
use App\Http\Middleware\TrackUserSession;
use Illuminate\Support\Facades\Route;
use Spatie\Sitemap\Sitemap;
use Spatie\Sitemap\Tags\Url;

/*
|--------------------------------------------------------------------------
| Front-end Routes
|--------------------------------------------------------------------------
|
| Here is where you can register front-end routes for your application.
|
*/

// Language switcher
Route::get('/lang/{locale}', [LanguageController::class, 'swap']);

// Main front-end routes with user session tracking
Route::middleware(TrackUserSession::class)->name('web.')->group(function () {

    // Auth routes
    Route::prefix('auth')->name('auth.')->group(function () {
        Route::controller(Front2AuthController::class)->group(function () {
            // Guest routes
            Route::middleware('guest')->group(function () {
                Route::get('/login', 'showLoginForm')->name('login');
                Route::post('/login', 'login');
                Route::get('/register', 'showRegistrationForm')->name('register');
                Route::post('/register', 'register');
                Route::get('/forgot-password', 'showForgotPasswordForm')->name('forgot-password');
                Route::post('/forgot-password', 'sendResetLinkEmail')->name('forgot-password');
                Route::get('/reset-password/{token}', 'showResetPasswordForm')->name('password.reset');
                Route::post('/reset-password', 'resetPassword')->name('reset-password');
            });

            // Auth routes
            Route::middleware('web.auth')->group(function () {
                Route::post('/logout', 'logout')->name('logout');
                Route::get('/email/verify', 'verificationNotice')->name('verification.notice');
                Route::post('/email/verification-notification', 'sendVerificationEmail')->name('verification.send');
            });

            // Verification route (special case)
            Route::get('/email/verify/{id}/{hash}', 'verifyEmail')
                ->middleware(['signed', 'throttle:6,1'])
                ->name('verification.verify');
        });
    });

    // Main front2 routes
    Route::controller(Front2Controller::class)->group(function () {
        // Public routes
        Route::get('/', 'index')->name('index');
        Route::get('/locale/{locale}', 'setLocale')->name('setLocale');
        Route::get('/theme/{theme}', 'setTheme')->name('setTheme');
        Route::get('/categories', 'categories')->name('categories');
        Route::get('/category/{slug}', 'category')->name('category');
        Route::get('/search', 'search')->name('search');
        Route::get('/product/{slug}', 'product')->name('product');
        Route::get('/seller/{username}', 'sellerProfile')->name('seller.profile');
        Route::get('/about', 'about')->name('about');
        Route::get('/contact', 'contactUs')->name('contactUs');
        Route::post('/newsletter/subscribe', 'subscribeNewsletter')->name('newsletter.subscribe');

        // Auth required routes
        Route::middleware(['web.auth'])->group(function () {
            Route::get('/favorites', 'favorites')->name('favorites');
            Route::post('/favorites/toggle/{productId}', 'toggleFavorite')->name('toggleFavorite');
            Route::get('/profile', 'profile')->name('profile');
            Route::get('/profile/edit', 'editProfile')->name('profile.edit');
            Route::put('/profile/update', 'updateProfile')->name('profile.update');
            Route::get('/profile/password', 'editPassword')->name('profile.password');
            Route::put('/profile/password', 'updatePassword')->name('profile.password.update');
            Route::get('/profile/products', 'profileProducts')->name('profile.products');
            Route::get('/profile/orders', 'profileOrders')->name('profile.orders');
            Route::get('/profile/notifications', 'profileNotifications')->name('profile.notifications');
            Route::post('/profile/notifications/mark-read', 'markNotificationsRead')->name('profile.notifications.mark-read');
            Route::get('/profile/verification', 'profileVerification')->name('profile.verification');
            Route::post('/profile/verification', 'submitVerification')->name('profile.verification.submit');
            Route::get('/profile/followed-products', 'profileFollowedProducts')->name('profile.followed_products');
            Route::post('/profile/toggle-follow', 'toggleFollow')->name('toggle.follow');
            Route::post('/profile/toggle-favorite/{productId}', 'profileToggleFavorite')->name('profileToggleFavorite');

            // User Commission routes
            Route::get('/profile/commissions', 'profileCommissions')->name('profile.commissions');
            Route::get('/profile/commissions/{id}', 'profileCommissionDetails')->name('profile.commissions.details');
            Route::post('/profile/commissions/{id}/accept', 'acceptCommission')->name('profile.commissions.accept');
            Route::post('/profile/commissions/{id}/reject', 'rejectCommission')->name('profile.commissions.reject');
            Route::post('/profile/commissions/{id}/complete', 'completeCommission')->name('profile.commissions.complete');
            Route::post('/profile/commissions/{id}/cancel', 'cancelCommission')->name('profile.commissions.cancel');

            // Product management routes
            Route::get('/profile/product/{id}/edit', 'editProduct')->name('editProduct');
            Route::put('/profile/product/{id}', 'updateProduct')->name('updateProduct');
            Route::delete('/profile/product/{id}', 'deleteProduct')->name('deleteProduct');
            Route::put('/profile/product/{id}/mark-sold', 'markProductSold')->name('markProductSold');
            Route::put('/profile/product/{id}/mark-deleted', 'markProductDeleted')->name('markProductDeleted');
            Route::post('/products/{productId}/commission', 'createCommission')->name('products.commission');
        });

        // Email verification required routes
        Route::middleware(['web.auth', 'verified'])->group(function () {
            Route::get('/products/create', 'createProduct')->name('products.create');
            Route::post('/products', 'storeProduct')->name('products.store');
        });
    });
});

// Static pages
Route::get('page/{slug}', [\App\Http\Controllers\PageController::class, 'open'])->name('page');

// Short URL redirector
Route::get('/srt/{shortCode}', [ShortUrlController::class, 'redirect']);

// Email verification and password reset pages
Route::get('/email/verify/success', function () {
    return view('auth.verification-success');
})->name('verification.success');

Route::get('/Reset-Pass/{token}', function ($token) {
    return view('auth.reset-password', ['token' => $token]);
})->name('password.reset.form');

Route::get('/password/reset/success', function () {
    return view('auth.reset-success');
})->name('password.reset.success');

// Robots.txt file
Route::get('/robots.txt', function () {
    $sitemapUrl = config('app.url') . '/sitemap.xml';

    $content = <<<EOT
    User-agent: *
    Disallow: /admin
    Disallow: /telescope
    Disallow: /horizon
    Disallow: /log-viewer
    Disallow: /queue
    Disallow: /profile
    Disallow: /short-urls
    Disallow: /uploads
    Disallow: /srt
    Disallow: /email/verify
    Disallow: /Reset-Pass

    # Allow public routes
    Allow: /
    Allow: /categories
    Allow: /category
    Allow: /search
    Allow: /product
    Allow: /seller
    Allow: /about
    Allow: /contact
    Allow: /blog
    Allow: /page

    Sitemap: {$sitemapUrl}
    EOT;

    return response($content, 200, ['Content-Type' => 'text/plain']);
})->name('robots.txt');

// Sitemap
Route::get('/sitemap.xml', function () {
    $sitemap = Sitemap::create()
        // Home page
        ->add(Url::create('/'))

        // Static pages
        ->add(Url::create('/about'))
        ->add(Url::create('/contact'))

        // Marketplace pages
        ->add(Url::create('/categories'))
        ->add(Url::create('/search'))

        // Blog
        ->add(Url::create('/blog'));

    // Add dynamic pages from database
    // Categories
    $categories = \App\Models\Category::where('status', true)->get();
    foreach ($categories as $category) {
        $sitemap->add(Url::create('/category/' . $category->slug));
    }

    // Static pages from database
    $pages = \App\Models\Page::where('status', true)->get();
    foreach ($pages as $page) {
        $sitemap->add(Url::create('/page/' . $page->slug));
    }

    // Blog posts
    if (class_exists('Modules\Blog\Models\Blog')) {
        $blogs = \Modules\Blog\Models\Blog::where('is_published', true)->get();
        foreach ($blogs as $blog) {
            $sitemap->add(Url::create('/blog/' . $blog->slug));
        }
    }

    // Products (limit to approved products, max 1000 for performance)
    $products = \App\Models\Product::where('status', \App\Enums\StatusEnum::APPROVED->value)
        ->orderBy('created_at', 'desc')
        ->limit(1000)
        ->get();
    foreach ($products as $product) {
        $sitemap->add(Url::create('/product/' . $product->slug));
    }

    // Seller profiles (only include users with approved products)
    $sellers = \App\Models\User::whereHas('products', function($query) {
        $query->where('status', \App\Enums\StatusEnum::APPROVED->value);
    })->limit(500)->get();

    foreach ($sellers as $seller) {
        $sitemap->add(Url::create('/seller/' . $seller->username));
    }

    return $sitemap->toResponse(request());
});
